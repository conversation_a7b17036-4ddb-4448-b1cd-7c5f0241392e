# 达梦数据库到MySQL数据同步系统使用指南

## 系统概述

本系统基于Kettle (PDI) 9.1实现，支持将达梦数据库中的所有表数据同步到MySQL数据库。系统已自动生成了以下10个表的同步转换：

1. **mpe_config** - 考试配置表
2. **mpe_examinfo** - 考试信息表  
3. **mpe_examsessioninfo** - 考试场次信息表
4. **mpe_examsubjectinfo** - 考试科目信息表
5. **mpp_deviceinfo** - 设备信息表
6. **mpp_exam_area** - 考区信息表
7. **mpp_orginfo** - 机构信息表
8. **mpu_userinfo** - 用户信息表
9. **vis_config** - 监控配置表
10. **vis_code** - 监控代码表

## 快速开始

### 第一步：配置数据库连接

编辑 `db_config.properties` 文件，设置正确的数据库连接参数：

```properties
# 达梦数据库配置
DM_HOST=your_dm_host
DM_PORT=5236
DM_USER=your_dm_user
DM_PASSWORD=your_dm_password

# MySQL数据库配置
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_DATABASE=jf_exvis_db
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
```

### 第二步：设置环境变量

```bash
source set_env.sh
```

### 第三步：运行数据同步

#### 选项1：全量同步所有表（推荐）
```bash
./run_sync.sh
```

#### 选项2：增量同步
```bash
./run_sync.sh -m incremental
```

#### 选项3：同步指定表
```bash
./run_sync.sh -t mpe_config -t mpe_examinfo
```

#### 选项4：试运行（测试配置）
```bash
./run_sync.sh --dry-run
```

## 详细使用说明

### 命令行选项

```bash
./run_sync.sh [选项]

选项:
    -h, --help              显示帮助信息
    -m, --mode MODE         同步模式: full(全量) 或 incremental(增量)
    -t, --table TABLE       同步指定表（可多次使用）
    -c, --config FILE       指定配置文件路径
    -d, --debug             启用调试模式
    -n, --dry-run           试运行模式（不执行实际同步）
    -p, --parallel NUM      并行作业数量
    -b, --batch-size NUM    批处理大小
    --check-connection      仅检查数据库连接
    --generate-ktr          重新生成KTR文件
    --list-tables           列出所有可同步的表
```

### 使用示例

1. **检查数据库连接**
   ```bash
   ./run_sync.sh --check-connection
   ```

2. **列出所有可同步的表**
   ```bash
   ./run_sync.sh --list-tables
   ```

3. **全量同步所有表**
   ```bash
   ./run_sync.sh
   ```

4. **增量同步（只同步变更数据）**
   ```bash
   ./run_sync.sh -m incremental
   ```

5. **同步特定表**
   ```bash
   ./run_sync.sh -t mpe_config -t mpe_examinfo -t mpp_orginfo
   ```

6. **调试模式运行**
   ```bash
   ./run_sync.sh -d
   ```

7. **自定义批处理大小和并行度**
   ```bash
   ./run_sync.sh -b 5000 -p 3
   ```

### 直接使用Kettle命令

如果你熟悉Kettle，也可以直接使用Kettle命令：

1. **运行主作业**
   ```bash
   kitchen.sh -file=dm_to_mysql_sync_job.kjb
   ```

2. **运行单个表同步**
   ```bash
   pan.sh -file=sync_mpe_config.ktr
   ```

3. **带参数运行**
   ```bash
   kitchen.sh -file=dm_to_mysql_sync_job.kjb -param:SYNC_MODE=INCREMENTAL
   ```

## 配置说明

### 同步模式

- **FULL（全量同步）**：
  - 清空目标表后重新导入所有数据
  - 适用于初始化或数据完整性要求高的场景
  - 执行时间较长，但数据最准确

- **INCREMENTAL（增量同步）**：
  - 只同步自上次同步以来的变更数据
  - 基于 `update_time` 字段判断数据变更
  - 执行速度快，适合定期同步

### 性能调优参数

- **BATCH_SIZE**：每次处理的记录数（默认10000）
  - 内存充足时可以增大，提高处理速度
  - 内存不足时应减小，避免内存溢出

- **COMMIT_SIZE**：事务提交间隔（默认1000）
  - 影响事务大小和回滚风险
  - 较小值更安全，较大值性能更好

- **PARALLEL_JOBS**：并行作业数量（默认5）
  - 根据CPU核数和数据库连接数调整
  - 过多可能导致资源竞争

## 监控和日志

### 日志文件位置

- 应用日志：`./logs/sync.log`
- Kettle日志：`./logs/kettle.log`
- 错误日志：`./logs/error.log`

### 监控指标

系统会记录以下监控信息：
- 同步开始和结束时间
- 处理的记录数量
- 成功/失败的表数量
- 错误信息和堆栈跟踪

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   [ERROR] 数据库连接参数不完整
   ```
   **解决方案**：检查 `db_config.properties` 中的连接参数

2. **Kettle命令未找到**
   ```
   [ERROR] KETTLE_HOME环境变量未设置
   ```
   **解决方案**：设置 `KETTLE_HOME` 环境变量指向Kettle安装目录

3. **KTR文件不存在**
   ```
   [ERROR] KTR文件不存在: sync_xxx.ktr
   ```
   **解决方案**：运行 `python3 generate_ktr_files.py` 重新生成KTR文件

4. **权限问题**
   ```
   Permission denied
   ```
   **解决方案**：确保脚本有执行权限 `chmod +x run_sync.sh`

### 调试步骤

1. **启用调试模式**
   ```bash
   ./run_sync.sh -d --dry-run
   ```

2. **检查配置文件**
   ```bash
   cat db_config.properties
   ```

3. **验证环境变量**
   ```bash
   source set_env.sh
   env | grep -E "(DM_|MYSQL_)"
   ```

4. **测试数据库连接**
   ```bash
   ./run_sync.sh --check-connection
   ```

## 高级功能

### 自定义表配置

如需添加新表或修改现有表配置：

1. 编辑 `table_sync_config.json`
2. 运行 `python3 generate_ktr_files.py` 重新生成KTR文件
3. 更新主作业文件中的表引用

### 定时同步

可以使用cron设置定时同步：

```bash
# 每天凌晨2点全量同步
0 2 * * * /path/to/run_sync.sh -m full

# 每小时增量同步
0 * * * * /path/to/run_sync.sh -m incremental
```

### 数据验证

系统支持数据验证功能：
- 记录数量对比
- 关键字段一致性检查
- 数据类型验证

## 性能建议

1. **网络优化**：确保数据库服务器间网络连接稳定且带宽充足
2. **索引优化**：在目标表的关键字段上创建索引
3. **分批处理**：对于大表，适当调整批处理大小
4. **并行控制**：根据系统资源合理设置并行度
5. **定期维护**：定期清理日志文件和临时数据

## 技术支持

如遇到问题，请按以下步骤操作：

1. 查看日志文件获取详细错误信息
2. 使用调试模式运行获取更多信息
3. 检查数据库连接和权限设置
4. 参考本文档的故障排除部分
5. 联系技术支持团队

---

**注意**：首次使用前请务必在测试环境中验证同步效果，确认无误后再在生产环境中使用。
