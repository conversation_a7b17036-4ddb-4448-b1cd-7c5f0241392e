<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>DM_to_MySQL_Sync</name>
    <description>达梦数据库到MySQL数据同步转换</description>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>DM_HOST</name>
        <default_value>localhost</default_value>
        <description>达梦数据库主机地址</description>
      </parameter>
      <parameter>
        <name>DM_PORT</name>
        <default_value>5236</default_value>
        <description>达梦数据库端口</description>
      </parameter>
      <parameter>
        <name>DM_DATABASE</name>
        <default_value>DAMENG</default_value>
        <description>达梦数据库名</description>
      </parameter>
      <parameter>
        <name>DM_USERNAME</name>
        <default_value>SYSDBA</default_value>
        <description>达梦数据库用户名</description>
      </parameter>
      <parameter>
        <name>DM_PASSWORD</name>
        <default_value>SYSDBA</default_value>
        <description>达梦数据库密码</description>
      </parameter>
      <parameter>
        <name>MYSQL_HOST</name>
        <default_value>localhost</default_value>
        <description>MySQL数据库主机地址</description>
      </parameter>
      <parameter>
        <name>MYSQL_PORT</name>
        <default_value>3306</default_value>
        <description>MySQL数据库端口</description>
      </parameter>
      <parameter>
        <name>MYSQL_DATABASE</name>
        <default_value>sync_db</default_value>
        <description>MySQL数据库名</description>
      </parameter>
      <parameter>
        <name>MYSQL_USERNAME</name>
        <default_value>root</default_value>
        <description>MySQL数据库用户名</description>
      </parameter>
      <parameter>
        <name>MYSQL_PASSWORD</name>
        <default_value>password</default_value>
        <description>MySQL数据库密码</description>
      </parameter>
      <parameter>
        <name>TABLE_NAME</name>
        <default_value>mpe_config</default_value>
        <description>要同步的表名</description>
      </parameter>
      <parameter>
        <name>SYNC_MODE</name>
        <default_value>FULL</default_value>
        <description>同步模式：FULL(全量) 或 INCREMENTAL(增量)</description>
      </parameter>
      <parameter>
        <name>BATCH_SIZE</name>
        <default_value>1000</default_value>
        <description>批处理大小</description>
      </parameter>
    </parameters>
    <log>
      <trans>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field/>
      </trans>
      <perf>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field/>
      </perf>
      <channel>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
      </channel>
      <step>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
      </step>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2024/07/30 09:00:00.000</created_date>
    <modified_user>-</modified_user>
    <modified_date>2024/07/30 09:00:00.000</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAADAAAA</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
    <notepad>
      <note>达梦数据库到MySQL数据同步转换

使用说明：
1. 配置数据库连接参数
2. 设置要同步的表名
3. 选择同步模式（全量/增量）
4. 运行转换

支持的同步模式：
- FULL: 全量同步，清空目标表后重新插入所有数据
- INCREMENTAL: 增量同步，基于update_time字段进行增量更新</note>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <width>400</width>
      <heigth>150</heigth>
      <fontname>Microsoft YaHei</fontname>
      <fontsize>9</fontsize>
      <fontbold>N</fontbold>
      <fontitalic>N</fontitalic>
      <fontcolorred>0</fontcolorred>
      <fontcolorgreen>0</fontcolorgreen>
      <fontcolorblue>0</fontcolorblue>
      <backgroundcolorred>255</backgroundcolorred>
      <backgroundcolorgreen>255</backgroundcolorgreen>
      <backgroundcolorblue>0</backgroundcolorblue>
      <bordercolorred>100</bordercolorred>
      <bordercolorgreen>100</bordercolorgreen>
      <bordercolorblue>100</bordercolorblue>
    </notepad>
  </notepads>
  <connection>
    <name>DM_Connection</name>
    <server>${DM_HOST}</server>
    <type>GENERIC</type>
    <access>Native</access>
    <database>${DM_DATABASE}</database>
    <port>${DM_PORT}</port>
    <username>${DM_USERNAME}</username>
    <password>Encrypted 2be98afc86aa7f2e4cb79ce10df90acde</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute><code>CUSTOM_DRIVER_CLASS</code><attribute>dm.jdbc.driver.DmDriver</attribute></attribute>
      <attribute><code>CUSTOM_URL</code><attribute>jdbc:dm://${DM_HOST}:${DM_PORT}/${DM_DATABASE}</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>${DM_PORT}</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>Y</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <connection>
    <name>MySQL_Connection</name>
    <server>${MYSQL_HOST}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${MYSQL_DATABASE}</database>
    <port>${MYSQL_PORT}</port>
    <username>${MYSQL_USERNAME}</username>
    <password>Encrypted 2be98afc86aa7f2e4cb79ce10df90acde</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute><code>EXTRA_OPTION_MYSQL.defaultFetchSize</code><attribute>500</attribute></attribute>
      <attribute><code>EXTRA_OPTION_MYSQL.useCursorFetch</code><attribute>true</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>${MYSQL_PORT}</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>N</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>STREAM_RESULTS</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <order>
    <hop> <from>Table input</from><to>Select values</to><enabled>Y</enabled> </hop>
    <hop> <from>Select values</from><to>Table output</to><enabled>Y</enabled> </hop>
  </order>
  <step>
    <name>Table input</name>
    <type>TableInput</type>
    <description>从达梦数据库读取数据</description>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>DM_Connection</connection>
    <sql>SELECT * FROM ${TABLE_NAME}
WHERE 1=1
${P(SYNC_MODE) == "INCREMENTAL" ? "AND update_time > (SELECT COALESCE(MAX(update_time), '1900-01-01') FROM " + P(TABLE_NAME) + "_sync_log)" : ""}</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema/>
    <remotesteps>
      <input>   </input>
      <output>   </output>
    </remotesteps>
    <GUI>
      <xloc>150</xloc>
      <yloc>250</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Select values</name>
    <type>SelectValues</type>
    <description>字段选择和转换</description>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>uid</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>ex_id</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>examiner_num</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>layout_time</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>examination_num</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>seatting_way</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>first_seating</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>is_seat_unify</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>is_seatway_unify</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>tour_rule</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>examinee_percent</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>city_opentime</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>county_opentime</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>exampoint_opentime</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>layout_number</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>layout_begin_time</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>parent_uid</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>enable_children_orglevel</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>examinee_data_status</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>status</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>create_by</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>create_time</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>update_by</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
      <field>
        <name>update_time</name>
        <rename/>
        <length>-2</length>
        <precision>-2</precision>
      </field>
    </fields>
    <cluster_schema/>
    <remotesteps>
      <input>   </input>
      <output>   </output>
    </remotesteps>
    <GUI>
      <xloc>350</xloc>
      <yloc>250</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Table output</name>
    <type>TableOutput</type>
    <description>写入MySQL数据库</description>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>MySQL_Connection</connection>
    <schema/>
    <table>${TABLE_NAME}</table>
    <commit>${BATCH_SIZE}</commit>
    <truncate>${P(SYNC_MODE) == "FULL" ? "Y" : "N"}</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>N</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field/>
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field/>
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field/>
    <fields>
    </fields>
    <cluster_schema/>
    <remotesteps>
      <input>   </input>
      <output>   </output>
    </remotesteps>
    <GUI>
      <xloc>550</xloc>
      <yloc>250</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
</transformation>