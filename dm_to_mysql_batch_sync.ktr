<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>达梦到MySQL批量数据同步</name>
    <description>批量将达梦数据库表数据同步到MySQL数据库</description>
    <extended_description>基于PDI 9.1版本，支持达梦数据库到MySQL的批量数据同步，包含错误处理和日志记录</extended_description>
    <trans_version>1</trans_version>
    <trans_type>Normal</trans_type>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>TABLE_LIST</name>
        <default_value>mpe_config,mpe_examinfo,mpe_examsessioninfo</default_value>
        <description>要同步的表列表，用逗号分隔</description>
      </parameter>
      <parameter>
        <name>BATCH_SIZE</name>
        <default_value>1000</default_value>
        <description>批处理大小</description>
      </parameter>
      <parameter>
        <name>ENABLE_TRUNCATE</name>
        <default_value>false</default_value>
        <description>是否清空目标表</description>
      </parameter>
      <parameter>
        <name>ENABLE_LOGGING</name>
        <default_value>true</default_value>
        <description>是否启用日志记录</description>
      </parameter>
    </parameters>
    <variables>
      <variable>
        <name>DM_HOST</name>
        <value>localhost</value>
        <description>达梦数据库主机</description>
      </variable>
      <variable>
        <name>DM_PORT</name>
        <value>5236</value>
        <description>达梦数据库端口</description>
      </variable>
      <variable>
        <name>DM_DATABASE</name>
        <value>DAMENG</value>
        <description>达梦数据库名</description>
      </variable>
      <variable>
        <name>DM_USERNAME</name>
        <value>SYSDBA</value>
        <description>达梦数据库用户名</description>
      </variable>
      <variable>
        <name>DM_PASSWORD</name>
        <value>SYSDBA</value>
        <description>达梦数据库密码</description>
      </variable>
      <variable>
        <name>MYSQL_HOST</name>
        <value>localhost</value>
        <description>MySQL数据库主机</description>
      </variable>
      <variable>
        <name>MYSQL_PORT</name>
        <value>3306</value>
        <description>MySQL数据库端口</description>
      </variable>
      <variable>
        <name>MYSQL_DATABASE</name>
        <value>target_db</value>
        <description>MySQL数据库名</description>
      </variable>
      <variable>
        <name>MYSQL_USERNAME</name>
        <value>root</value>
        <description>MySQL数据库用户名</description>
      </variable>
      <variable>
        <name>MYSQL_PASSWORD</name>
        <value>password</value>
        <description>MySQL数据库密码</description>
      </variable>
    </variables>
    <notepads>
      <notepad>
        <note>达梦到MySQL批量数据同步转换
功能特性：
1. 支持批量同步多个表
2. 自动字段映射
3. 错误处理和日志记录
4. 可配置的批处理大小
5. 支持数据过滤条件
6. 支持目标表清空选项

使用说明：
1. 配置数据库连接参数
2. 设置要同步的表列表
3. 配置批处理大小
4. 选择是否清空目标表
5. 启用日志记录功能</note>
      </notepad>
    </notepads>
    <order>
      <hop>
        <from>获取表列表</from>
        <to>表输入</to>
        <enabled>Y</enabled>
      </hop>
      <hop>
        <from>表输入</from>
        <to>字段选择</to>
        <enabled>Y</enabled>
      </hop>
      <hop>
        <from>字段选择</from>
        <to>表输出</to>
        <enabled>Y</enabled>
      </hop>
      <hop>
        <from>表输出</from>
        <to>写日志</to>
        <enabled>Y</enabled>
      </hop>
    </order>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>获取表列表</from>
      <to>表输入</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>表输入</from>
      <to>字段选择</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>表输出</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>表输出</from>
      <to>写日志</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>获取表列表</name>
    <type>GetTableNames</type>
    <description>获取要同步的表列表</description>
    <distribute>Y</distribute>
    <custom_distribution>
    </custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
      <number_partitions>1</number_partitions>
      <number_partition_fields>0</number_partition_fields>
    </partitioning>
    <parameters>
    </parameters>
    <notepads>
    </notepads>
    <cluster_schema>
    </cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>50</xloc>
      <yloc>50</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>表输入</name>
    <type>TableInput</type>
    <description>从达梦数据库读取数据</description>
    <distribute>Y</distribute>
    <custom_distribution>
    </custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
      <number_partitions>1</number_partitions>
      <number_partition_fields>0</number_partition_fields>
    </partitioning>
    <connection>达梦数据库连接</connection>
    <sql>SELECT * FROM ${TABLE_NAME}
WHERE status='A'</sql>
    <limit>0</limit>
    <lookup>
      <key>
      </key>
      <add>
      </add>
      <add_stream>
      </add_stream>
      <add_stream_field>
      </add_stream_field>
      <add_field>
      </add_field>
      <add_field_name>
      </add_field_name>
      <add_field_rename>
      </add_field_rename>
      <add_field_type>
      </add_field_type>
      <add_field_length>
      </add_field_length>
      <add_field_precision>
      </add_field_precision>
      <add_field_currency>
      </add_field_currency>
      <add_field_decimal>
      </add_field_decimal>
      <add_field_group>
      </add_field_group>
      <add_field_nullif>
      </add_field_nullif>
      <add_field_default>
      </add_field_default>
      <add_field_trim_type>
      </add_field_trim_type>
    </lookup>
    <parameters>
    </parameters>
    <notepads>
    </notepads>
    <cluster_schema>
    </cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>250</xloc>
      <yloc>50</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择</name>
    <type>SelectValues</type>
    <description>字段映射和数据类型转换</description>
    <distribute>Y</distribute>
    <custom_distribution>
    </custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
      <number_partitions>1</number_partitions>
      <number_partition_fields>0</number_partition_fields>
    </partitioning>
    <select>
      <field>
        <name>uid</name>
        <rename>uid</rename>
        <type>String</type>
        <length>128</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>ex_id</name>
        <rename>ex_id</rename>
        <type>String</type>
        <length>120</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>org_id</name>
        <rename>org_id</rename>
        <type>String</type>
        <length>136</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>name</name>
        <rename>name</rename>
        <type>String</type>
        <length>400</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>status</name>
        <rename>status</rename>
        <type>String</type>
        <length>4</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>create_by</name>
        <rename>create_by</rename>
        <type>String</type>
        <length>128</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>create_time</name>
        <rename>create_time</rename>
        <type>Date</type>
        <length>-1</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>update_by</name>
        <rename>update_by</rename>
        <type>String</type>
        <length>128</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
      <field>
        <name>update_time</name>
        <rename>update_time</rename>
        <type>Date</type>
        <length>-1</length>
        <precision>-1</precision>
        <currency>
        </currency>
        <decimal>
        </decimal>
        <group>
        </group>
        <nullif>
        </nullif>
        <ifnull>
        </ifnull>
        <trim_type>none</trim_type>
      </field>
    </select>
    <remove>
    </remove>
    <meta>
    </meta>
    <parameters>
    </parameters>
    <notepads>
    </notepads>
    <cluster_schema>
    </cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>450</xloc>
      <yloc>50</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>表输出</name>
    <type>TableOutput</type>
    <description>写入MySQL数据库</description>
    <distribute>Y</distribute>
    <custom_distribution>
    </custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
      <number_partitions>1</number_partitions>
      <number_partition_fields>0</number_partition_fields>
    </partitioning>
    <connection>MySQL数据库连接</connection>
    <table>${TABLE_NAME}</table>
    <schema>
    </schema>
    <commit>1000</commit>
    <truncate>${ENABLE_TRUNCATE}</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <batch_size>${BATCH_SIZE}</batch_size>
    <specify_fields>N</specify_fields>
    <field>
      <name>uid</name>
      <stream_name>uid</stream_name>
    </field>
    <field>
      <name>ex_id</name>
      <stream_name>ex_id</stream_name>
    </field>
    <field>
      <name>org_id</name>
      <stream_name>org_id</stream_name>
    </field>
    <field>
      <name>name</name>
      <stream_name>name</stream_name>
    </field>
    <field>
      <name>status</name>
      <stream_name>status</stream_name>
    </field>
    <field>
      <name>create_by</name>
      <stream_name>create_by</stream_name>
    </field>
    <field>
      <name>create_time</name>
      <stream_name>create_time</stream_name>
    </field>
    <field>
      <name>update_by</name>
      <stream_name>update_by</stream_name>
    </field>
    <field>
      <name>update_time</name>
      <stream_name>update_time</stream_name>
    </field>
    <parameters>
    </parameters>
    <notepads>
    </notepads>
    <cluster_schema>
    </cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>650</xloc>
      <yloc>50</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写日志</name>
    <type>WriteToLog</type>
    <description>记录同步日志</description>
    <distribute>Y</distribute>
    <custom_distribution>
    </custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
      <number_partitions>1</number_partitions>
      <number_partition_fields>0</number_partition_fields>
    </partitioning>
    <logmessage>表 ${TABLE_NAME} 同步完成，处理记录数：${RECORDS_PROCESSED}</logmessage>
    <loglevel>Basic</loglevel>
    <logsubject>DATA</logsubject>
    <parameters>
    </parameters>
    <notepads>
    </notepads>
    <cluster_schema>
    </cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>850</xloc>
      <yloc>50</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <connection>
    <name>达梦数据库连接</name>
    <server>${DM_HOST}</server>
    <type>DAMENG</type>
    <access>Native</access>
    <database>${DM_DATABASE}</database>
    <port>${DM_PORT}</port>
    <user>${DM_USERNAME}</user>
    <pass>${DM_PASSWORD}</pass>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_DAMENG_characterEncoding</code>
        <attribute>UTF-8</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_DAMENG_useUnicode</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_DAMENG_autoReconnect</code>
        <attribute>true</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>MySQL数据库连接</name>
    <server>${MYSQL_HOST}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${MYSQL_DATABASE}</database>
    <port>${MYSQL_PORT}</port>
    <user>${MYSQL_USERNAME}</user>
    <pass>${MYSQL_PASSWORD}</pass>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL_characterEncoding</code>
        <attribute>utf8</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL_useUnicode</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL_autoReconnect</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL_useSSL</code>
        <attribute>false</attribute>
      </attribute>
    </attributes>
  </connection>
</transformation> 